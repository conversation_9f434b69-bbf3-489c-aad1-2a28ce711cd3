import { useState, useCallback, useRef, useEffect } from '@max/max';
import { MRN_GET__dzim_pilot_assistant_hair_task_submit } from '@APIs/MRN_GET__dzim_pilot_assistant_hair_task_submit';
import { MRN_GET__dzim_pilot_assistant_beauty_task } from '@APIs/MRN_GET__dzim_pilot_assistant_beauty_task';
import { REQUEST_PLATFORM } from '@/utils';

// 款式生成状态枚举
export enum StyleGenerationStatus {
  NOT_GENERATED = 'not_generated',    // 未生成
  GENERATING = 'generating',          // 生成中
  GENERATED = 'generated',            // 已生成
  FAILED = 'failed'                   // 生成失败
}

// 款式状态管理接口
export interface StyleState {
  status: StyleGenerationStatus;      // 当前状态
  generatedImageUrl?: string;         // 生成的图片URL
  taskId?: number;                    // 任务ID
  error?: string;                     // 错误信息
  lastGeneratedTime?: number;         // 最后生成时间戳
}

// 轮询任务管理接口
interface PollingTask {
  taskId: number;
  timer: NodeJS.Timeout | null;
}

// Hook返回值接口
export interface UseStyleGenerationReturn {
  // 当前状态
  status: StyleGenerationStatus;
  // 生成的图片URL
  generatedImageUrl?: string;
  // 任务ID
  taskId?: number;
  // 错误信息
  error?: string;
  // 开始生成函数
  startGeneration: (options: StartGenerationOptions) => Promise<void>;
  // 继续轮询函数（用于已有taskId的情况）
  continuePolling: (options: ContinuePollingOptions) => Promise<void>;
  // 显示生成结果函数
  showGenerationResult: (options: ShowGenerationResultOptions) => void;
  // 清理函数
  cleanup: () => void;
  // 重置状态函数
  reset: () => void;
}

interface StartGenerationOptions {
  originalImageUrl: string;
  styleImageUrl: string;
  type: number;
  onStateChange?: (state: Partial<StyleState>) => void;
}

interface ContinuePollingOptions {
  taskId: number;
  onStateChange?: (state: Partial<StyleState>) => void;
}

interface ShowGenerationResultOptions {
  generatedImageUrl: string;
  onStateChange?: (state: Partial<StyleState>) => void;
}

/**
 * 管理单个款式生成和轮询逻辑的自定义Hook
 */
export function useStyleGeneration(): UseStyleGenerationReturn {
  // 状态管理
  const [styleState, setStyleState] = useState<StyleState>({
    status: StyleGenerationStatus.NOT_GENERATED
  });

  // 轮询任务管理
  const currentPollingTask = useRef<PollingTask | null>(null);

  // 更新状态的辅助函数
  const updateState = useCallback((updates: Partial<StyleState>, callback?: (next: StyleState) => void) => {
    setStyleState(prev => {
      const next = { ...prev, ...updates };
      callback?.(next);
      return next;
    });
  }, []);

  // 清理当前轮询任务
  const cleanup = useCallback(() => {
    if (currentPollingTask.current?.timer) {
      clearTimeout(currentPollingTask.current.timer);
    }
    currentPollingTask.current = null;
  }, []);

  // 轮询获取任务结果
  const pollTaskResult = useCallback(async (
    taskId: number,
    maxAttempts: number = 15
  ): Promise<string> => {
    let attempts = 0;

    // 设置当前轮询任务
    currentPollingTask.current = { taskId, timer: null };

    try {
      while (attempts < maxAttempts) {
        // 检查是否被取消
        if (!currentPollingTask.current) {
          return '';
        }

        try {
          const result = await MRN_GET__dzim_pilot_assistant_beauty_task({
            taskId,
            platform: REQUEST_PLATFORM,
          });

          if (result.status === 2 && result.result) {
            // 处理成功，返回结果URL
            return result.result;
          } else if (result.status === 3) {
            // 处理失败
            throw new Error('AI生成失败');
          }
          // 状态为1（处理中），继续轮询
          attempts++;
          if (attempts < maxAttempts) {
            await new Promise(resolve => {
              if (currentPollingTask.current) {
                currentPollingTask.current.timer = setTimeout(resolve, 2000);
              }
            }); // 等待2秒后重试
          }
        } catch (error) {
          console.error('轮询任务结果失败:', error);
          throw error;
        }
      }

      throw new Error('AI生成超时');
    } finally {
      // 清理轮询任务引用
      cleanup();
    }
  }, [cleanup]);

  // 开始生成函数
  const startGeneration = useCallback(async (options: StartGenerationOptions): Promise<void> => {
    const { originalImageUrl, styleImageUrl, type, onStateChange } = options;
    try {
      console.log('更新状态为生成中');
      // 更新状态为生成中
      updateState({
        status: StyleGenerationStatus.GENERATING,
        error: undefined
      }, onStateChange);

      // 1. 提交任务
      const submitResult = await MRN_GET__dzim_pilot_assistant_hair_task_submit({
        type: String(type),
        platform: REQUEST_PLATFORM,
        styleUrl: styleImageUrl,
        originUrl: originalImageUrl,
      });

      if (!submitResult.taskId) {
        throw new Error('任务提交失败');
      }

      console.log('更新任务ID', submitResult.taskId);
      // 更新任务ID
      updateState({
        status: StyleGenerationStatus.GENERATING,
        taskId: submitResult.taskId,
        error: undefined
      }, onStateChange);

      // 2. 轮询获取结果
      const generatedImageUrl = await pollTaskResult(submitResult.taskId);

      console.log('更新状态为已生成', generatedImageUrl);
      // 更新状态为已生成
      updateState({
        status: StyleGenerationStatus.GENERATED,
        generatedImageUrl: generatedImageUrl || ''
      }, onStateChange);
    } catch (error) {
      console.error('发型生成失败:', error);

      // 更新状态为失败
      updateState({
        status: StyleGenerationStatus.FAILED,
        error: error instanceof Error ? error.message : '生成失败'
      }, onStateChange);

      throw error;
    }
  }, [updateState, pollTaskResult]);

  // 继续轮询函数（用于已有taskId的情况）
  const continuePolling = useCallback(async (options: ContinuePollingOptions): Promise<void> => {
    const { taskId, onStateChange } = options;
    try {
      // 更新状态为生成中
      updateState({
        status: StyleGenerationStatus.GENERATING,
        taskId,
        error: undefined,
      }, onStateChange);

      // 轮询获取结果
      const generatedImageUrl = await pollTaskResult(taskId);

      // 更新状态为已生成
      updateState({
        status: StyleGenerationStatus.GENERATED,
        generatedImageUrl,
        lastGeneratedTime: Date.now(),
      }, onStateChange);

    } catch (error) {
      console.error('轮询失败:', error);

      // 更新状态为失败
      updateState({
        status: StyleGenerationStatus.FAILED,
        error: error instanceof Error ? error.message : '轮询失败'
      }, onStateChange);

      throw error;
    }
  }, [updateState, pollTaskResult]);

  const showGenerationResult = useCallback((options: ShowGenerationResultOptions) => {
    const { generatedImageUrl, onStateChange } = options;
    updateState({
      status: StyleGenerationStatus.GENERATED,
      generatedImageUrl,
      lastGeneratedTime: Date.now()
    }, onStateChange);
  }, [updateState]);

  // 重置状态函数
  const reset = useCallback(() => {
    cleanup();
    setStyleState({
      status: StyleGenerationStatus.NOT_GENERATED
    });
  }, [cleanup]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      cleanup();
    };
  }, [cleanup]);

  return {
    status: styleState.status,
    generatedImageUrl: styleState.generatedImageUrl,
    taskId: styleState.taskId,
    error: styleState.error,
    startGeneration,
    continuePolling,
    showGenerationResult,
    cleanup,
    reset,
  };
}
