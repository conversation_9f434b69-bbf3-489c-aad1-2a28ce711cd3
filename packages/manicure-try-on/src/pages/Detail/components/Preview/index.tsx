import { createElement, memo, useState, useCallback, forwardRef, useImperativeHandle } from '@max/max';
import { remStyleSheet } from '@max/leez-style-util';
import View from '@hfe/max-view';
import Image from '@hfe/max-image';
import LText from '@max/leez-text';
import Toast from '@max/leez-toast';
import BlurView from '@max/leez-blur-view';
import LoadingView from '@max/leez-loading-view';
import { getSystemInfoSync } from '@max/meituan-uni-system';
import { StyleImagesItems } from '@APIs/MRN_GET__dzim_pilot_assistant_beauty_styles';
import LikeDislikeButtons from '@/components/LikeDislikeButtons';
import StyleInfoCard from '@/components/StyleInfoCard';
import SaveImageModal from '@/components/SaveImageModal';
import { useStyleGeneration, StyleGenerationStatus } from '../../hooks/useStyleGeneration';

const { screenWidth } = getSystemInfoSync();
const IMAGE_WIDTH = screenWidth;
const IMAGE_HEIGHT = IMAGE_WIDTH * 1040 / 750;
const IMAGE_STYLE = { width: IMAGE_WIDTH, height: IMAGE_HEIGHT };

// 款式状态管理接口
export interface StyleState {
  status: StyleGenerationStatus;      // 当前状态
  styleImageUrl?: string;             // 款式图片URL
  type?: number;                      // 试戴类型，1-试发型，2-试美甲，3-试染发
  generatedImageUrl?: string;         // 生成的图片URL
  taskId?: number;                    // 任务ID
  error?: string;                     // 错误信息
  lastGeneratedTime?: number;         // 最后生成时间戳
}

export interface PreviewTriggerParams {
  styleImageUrl?: string;
  type?: number;
  taskId?: number;
  generatedImageUrl?: string;
  onStateUpdate?: (state: Partial<StyleState>) => void;
}

interface PreviewProps {
  // 原始图片URL
  originalImageUrl: string;
  // 当前选中的款式信息（null表示原图）
  currentStyle?: StyleImagesItems | null;
  // 是否是原图
  isOriginal?: boolean;
}

// Preview组件暴露的方法接口
export interface PreviewRef {
  trigger: (action: 'reset' | 'generate' | 'poll' | 'show', params: PreviewTriggerParams) => Promise<void>;
}

const Preview = forwardRef<PreviewRef, PreviewProps>((props, ref) => {
  const {
    originalImageUrl,
    currentStyle,
    isOriginal = false,
  } = props;

  const [showSaveModal, setShowSaveModal] = useState(false);

  // 使用自定义Hook管理款式生成
  const {
    status,
    generatedImageUrl,
    taskId,
    error,
    startGeneration,
    continuePolling,
    showGenerationResult,
    reset,
  } = useStyleGeneration();

  // 获取当前显示的图片URL
  const getCurrentImageUrl = useCallback(() => {
    if (isOriginal) {
      return originalImageUrl; // 原图
    }

    if (status === StyleGenerationStatus.GENERATED && generatedImageUrl) {
      return generatedImageUrl;
    }

    return originalImageUrl; // 默认显示原图
  }, [isOriginal, status, generatedImageUrl, originalImageUrl]);

  const isLoading = status === StyleGenerationStatus.GENERATING;
  const isComplete = status === StyleGenerationStatus.GENERATED;
  const isFailed = status === StyleGenerationStatus.FAILED;

  // 暴露trigger方法给父组件
  useImperativeHandle(ref, () => ({
    trigger: async (action: 'reset' | 'generate' | 'poll' | 'show', params: PreviewTriggerParams) => {
      try {
        switch (action) {
          case 'reset':
            // 重置状态
            reset();
            break;

          case 'generate':
            // 首次生成
            reset();
            await startGeneration({
              originalImageUrl,
              styleImageUrl: params?.styleImageUrl || '',
              type: params?.type || 1,
              onStateChange: (state: Partial<StyleState>) => {
                if (params?.onStateUpdate) {
                  params.onStateUpdate(state);
                }
              }
            });
            break;

          case 'poll':
            // 继续轮询
            if (params.taskId) {
              await continuePolling({
                taskId: params.taskId,
                onStateChange: (state: Partial<StyleState>) => {
                  if (params?.onStateUpdate) {
                    params.onStateUpdate(state);
                  }
                }
              });
            }
            break;

          case 'show':
            showGenerationResult({
              generatedImageUrl: params.generatedImageUrl || '',
              onStateChange: (state: Partial<StyleState>) => {
                if (params?.onStateUpdate) {
                  params.onStateUpdate(state);
                }
              }
            });
            break;

          default:
            console.warn(`Unknown action: ${action}`);
        }
      } catch (error) {
        console.error(`Error in trigger action ${action}:`, error);
        if (params?.onStateUpdate) {
          params.onStateUpdate({
            status: StyleGenerationStatus.FAILED,
            error: error instanceof Error ? error.message : '操作失败'
          });
        }
        Toast.open({ title: 'AI生成失败，请重试' });
      }
    }
  }), [reset, startGeneration, continuePolling, taskId, generatedImageUrl, showGenerationResult]);

  return (
    <View style={[styles.container, IMAGE_STYLE]}>
      {/* 主图片 */}
      <Image style={IMAGE_STYLE} source={{ uri: getCurrentImageUrl() }} resizeMode="cover" />

      {/* 完成态展示 */}
      {isComplete && (
        <>
          {/* 点赞点踩按钮 */}
          <LikeDislikeButtons taskId={taskId} />

          {/* 闪亮效果 */}
          <Image
            style={styles.sparkleEffect}
            source={{ uri: '' }} // 暂时使用空字符串占位
            resizeMode="contain"
          />
        </>
      )}

      {/* 加载状态覆盖层 */}
      {isLoading && (
        <>
          <BlurView
            style={styles.loadingOverlay}
            blurType="light"
            blurAmount={5}
          />
          <View style={styles.loadingView}>
            <View style={styles.loadingWrap}>
              <LoadingView level="large" content="正在模拟效果" bgColorType="dark" />
            </View>
          </View>
        </>
      )}

      {/* 失败状态覆盖层 */}
      {isFailed && (
        <>
          <BlurView
            style={styles.loadingOverlay}
            blurType="light"
            blurAmount={3}
          />
          <View style={styles.loadingView}>
            <View style={styles.errorWrap}>
              <Image
                style={styles.errorIcon}
                source={{ uri: 'https://p0.meituan.net/joyplaystatic/03a3ee04192d638227d5b6bc2176844e2106.png' }}
              />
              <LText style={styles.errorText}>
                {error || 'AI生成失败'}
              </LText>
              <LText style={styles.retryHint}>
                请重新选择款式重试
              </LText>
            </View>
          </View>
        </>
      )}

      {/* 款式信息介绍模块 - 放在主图片下方 */}
      {isComplete && currentStyle && (
        <StyleInfoCard
          style={styles.styleInfoCard}
          title={currentStyle.title}
          tags={currentStyle.tags}
        />
      )}

      {/* 保存图片弹窗 */}
      <SaveImageModal
        visible={showSaveModal}
        onClose={() => setShowSaveModal(false)}
        imageUrl={getCurrentImageUrl()}
      />
    </View>
  );
});

const styles = remStyleSheet({
  container: {
    position: 'relative',
  },
  sparkleEffect: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    width: 100,
    height: 100,
    marginTop: -50,
    marginLeft: -50,
    zIndex: 5,
  },
  loadingOverlay: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  },
  loadingView: {
    width: '100%',
    height: '100%',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingWrap: {
    backgroundColor: 'rgba(85, 85, 85, 0.5)',
    paddingVertical: 15,
    paddingHorizontal: 10,
    borderRadius: 10,
  },
  styleInfoCard: {
    position: 'absolute',
    bottom: 22,
    left: 0,
    right: 0,
  },
  errorWrap: {
    backgroundColor: 'rgba(85, 85, 85, 0.5)',
    paddingVertical: 20,
    paddingHorizontal: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  errorIcon: {
    width: 32,
    height: 32,
    marginBottom: 8,
  },
  errorText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
    textAlign: 'center',
  },
  retryHint: {
    color: '#fff',
    fontSize: 14,
    opacity: 0.8,
    textAlign: 'center',
  },
});

export default memo(Preview);