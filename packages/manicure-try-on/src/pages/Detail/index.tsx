import { createElement, memo, useCallback, useState, useEffect, useRef } from '@max/max';
import { remStyleSheet } from '@max/leez-style-util';
import View from '@hfe/max-view';
import LText from '@max/leez-text';
import NavigationBar from '@max/leez-navigation-bar';
import { navigateBack } from '@max/meituan-uni-navigate';
import TopViewProvider from '@max/leez-top-view-provider';
import ToastManager from '@max/leez-toast-manager';
import DialogManager from '@max/leez-dialog-manager';
import LoadingManager from '@max/leez-loading-manager';

import { MRN_GET__dzim_pilot_assistant_beauty_styles, Query } from '@APIs/MRN_GET__dzim_pilot_assistant_beauty_styles';
import { StyleGenerationStatus, StyleState } from '../../hooks/useStyleGeneration';

import Preview, { PreviewRef } from '../../components/Preview/index';
import StyleList, { StyleImage } from '../../components/StyleList/index';


interface DetailProps {
  type: Query['type'];
  originalUrl: string;
}

function Detail(props: DetailProps) {
  const { type = 1, originalUrl = '' } = props;

  // 基础状态
  const [styleList, setStyleList] = useState<StyleImage[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);

  // 款式状态管理 - 为每个款式维护独立状态
  const [styleStates, setStyleStates] = useState<Map<number, StyleState>>(new Map());

  // Preview组件的引用，用于调用trigger方法
  const previewRef = useRef<PreviewRef>(null);

  // 更新款式状态
  const updateStyleState = useCallback((styleIndex: number, updates: Partial<StyleState>) => {
    setStyleStates(prev => {
      const newStates = new Map(prev);
      const currentState = newStates.get(styleIndex) || { status: StyleGenerationStatus.NOT_GENERATED };
      newStates.set(styleIndex, { ...currentState, ...updates });
      return newStates;
    });
  }, []);

  // 获取款式状态
  const getStyleState = useCallback((styleIndex: number): StyleState => {
    return styleStates.get(styleIndex) || { status: StyleGenerationStatus.NOT_GENERATED };
  }, [styleStates]);

  const onBackPress = useCallback(() => {
    navigateBack();
  }, []);

  const onActionPress = useCallback((index: number) => {
    console.log('onActionPress', index);
  }, []);

  // 处理款式切换
  const handleStyleSelect = useCallback(async (item: StyleImage, index: number) => {
    if (index === currentIndex) {
      return;
    }

    // 切换到新的款式
    setCurrentIndex(index);
    console.log(`切换到款式: ${index === 0 ? '原图' : item.title}`, item);

    // 如果切换到原图，直接返回
    if (index === 0) {
      // 调用Preview的trigger方法重置状态
      if (previewRef.current) {
        await previewRef.current.trigger('reset', {});
      }
      return;
    }

    // 获取当前款式的状态
    const current = getStyleState(index);

    // 根据款式状态决定处理策略
    if (current.status === StyleGenerationStatus.NOT_GENERATED || current.status === StyleGenerationStatus.FAILED) {
      // 首次切换到该款式：调用Preview的trigger方法触发生成
      // 或者切换到失败款式：调用Preview的trigger方法重新生成
      console.log(`首次切换到款式: ${item.title}，触发生成`);
      if (previewRef.current) {
        await previewRef.current.trigger('generate', {
          styleImageUrl: item.styleImageUrl || '',
          type,
          onStateUpdate: (updates: Partial<StyleState>) => {
            console.log('更新状态', updates);
            updateStyleState(index, updates);
          }
        });
      }
    } else if (current.status === StyleGenerationStatus.GENERATING) {
      // 生成中：调用Preview的trigger方法继续轮询
      console.log(`切换到生成中款式: ${item.title}，继续轮询`);
      if (previewRef.current && current.taskId) {
        await previewRef.current.trigger('poll', {
          taskId: current.taskId,
          onStateUpdate: (updates: Partial<StyleState>) => {
            updateStyleState(index, updates);
          }
        });
      }
    } else if (current.status === StyleGenerationStatus.GENERATED) {
      // 已生成过：调用Preview的trigger方法查询状态和结果
      console.log(`切换到已生成款式: ${item.title}，查询结果`);
      if (previewRef.current) {
        await previewRef.current.trigger('show', {
          generatedImageUrl: current.generatedImageUrl || '',
        });
      }
    }
  }, [currentIndex, originalUrl, type, getStyleState, updateStyleState]);

  // 初始化数据
  useEffect(() => {
    const initData = async () => {
      try {
        const response = await MRN_GET__dzim_pilot_assistant_beauty_styles({ type });
        if (Array.isArray(response.styleImages) && response.styleImages.length > 0) {
          setStyleList(response.styleImages);
        }
      } catch (error) {
        console.error('Failed to fetch style data:', error);
      }
    };
    initData();
  }, [type]);

  return (
    <>
      <TopViewProvider>
        <View style={styles.container}>
          <NavigationBar
            style={{ backgroundColor: '#fff' }}
            safeArea='normal'
            backIcon={{ name: 'fanhui' }}
            actionIcons={[{ name: 'fenxiang' }]}
            onBackPress={onBackPress}
            onActionPress={onActionPress}
          >
            <LText type='title3' lineClamp={1}>AI试发型</LText>
          </NavigationBar>

          {/* 预览图 */}
          <Preview ref={previewRef} originalImageUrl={originalUrl} isOriginal={currentIndex === 0} />
          {/* 款式列表 */}
          <StyleList
            list={styleList}
            selectedIndex={currentIndex}
            onStyleSelect={handleStyleSelect}
          />
        </View>
      </TopViewProvider>
      <ToastManager />
      <DialogManager />
      <LoadingManager />
    </>
  );
}

const styles = remStyleSheet({
  container: {
    flex: 1,
    flexDirection: 'column',
    height: '100%',
    backgroundColor: '#fff',
  },
});

export default memo(Detail);
